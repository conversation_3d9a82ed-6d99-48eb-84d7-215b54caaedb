import React from 'react'
import { createRoot } from 'react-dom/client'
import type { Root } from 'react-dom/client'
import { EFloatButtonActionType, EContentsMessageType } from '@src/common/const'
import SelectionBarComponent from '../components/SelectionBar/index'
import AIProcessModalComponent from '../components/AIProcessModal/index'
import { getFloatingButton } from '../components/FloatButton'
import HiddenChatUI, { type HiddenChatUIRef } from '../components/HiddenChatUI/index'

// 导入样式文本用于注入到 Shadow DOM
import selectionBarStyleText from 'data-text:../components/SelectionBar/index.module.less'
import floatButtonStyleText from 'data-text:../components/FloatButton/index.module.less'
import aiProcessModalStyleText from 'data-text:../components/AIProcessModal/index.module.less'
import secondaryActionButtonsStyleText from 'data-text:../components/SecondaryActionButtons/index.module.less'
import hiddenChatUIStyleText from 'data-text:../components/HiddenChatUI/index.module.less'
import { handleStartTranslate } from './injectTranslate'

import { configShowFloatingButton } from '../utils/setting'

/**
 * Web Assistant 主管理器
 * 统一管理划词工具栏和浮动按钮
 */
class WebAssistantManager {
  private mainContainer: HTMLDivElement | null = null
  private shadowRoot: ShadowRoot | null = null
  private selectionBarContainer: HTMLDivElement | null = null
  private floatingButtonContainer: HTMLDivElement | null = null
  private aiProcessModalContainer: HTMLDivElement | null = null
  private selectionBarRoot: Root | null = null
  private floatingButtonRoot: Root | null = null
  private aiProcessModalRoot: Root | null = null

  // 隐藏ChatUI相关属性
  private hiddenChatUIContainer: HTMLDivElement | null = null
  private hiddenChatUIRoot: Root | null = null
  private hiddenChatUIRef: React.RefObject<HiddenChatUIRef> = React.createRef()

  private selectedText: string = ''
  private isSelectionBarVisible: boolean = false
  private isFloatingButtonVisible: boolean = true
  private isAIProcessModalVisible: boolean = false

  // AIProcessModal相关状态
  private currentAIAction: string = ''
  private aiProcessModalDataUpdater: ((data: string, isComplete: boolean, conversationId?: string) => void) | null = null
  private cachedSelectionRect: DOMRect | null = null // 缓存选择区域的位置信息

  constructor() {
    this.init()
  }

  private init(): void {
    try {
      this.createMainContainer()
      this.initEventListeners()
      this.initFloatingButton()
      this.initHiddenChatUI()
    } catch (error) {
      console.error('WebAssistantManager: Initialization failed:', error)
    }
  }

  /**
   * 创建主容器和 Shadow DOM
   */
  private createMainContainer(): void {

    // 检查是否已经存在主容器，避免重复创建
    const existingContainer = document.getElementById(
      'web-assistant-main-container'
    )
    if (existingContainer) {
      return
    }

    // 创建主容器
    this.mainContainer = document.createElement('div')
    this.mainContainer.id = 'web-assistant-main-container'
    this.mainContainer.className = 'web-assistant-container'

    // 创建 Shadow Root
    this.shadowRoot = this.mainContainer.attachShadow({ mode: 'open' })

    // 添加样式
    const style = document.createElement('style')
    style.textContent =
      this.getStyles() +
      '\n' +
      selectionBarStyleText +
      '\n' +
      floatButtonStyleText +
      '\n' +
      aiProcessModalStyleText +
      '\n' +
      secondaryActionButtonsStyleText +
      '\n' +
      hiddenChatUIStyleText
    this.shadowRoot.appendChild(style)

    // 创建划词工具栏容器
    this.selectionBarContainer = document.createElement('div')
    this.selectionBarContainer.id = 'web-assistant-selection-bar'
    this.selectionBarContainer.className =
      'web-assistant-selection-bar-container'
    this.shadowRoot.appendChild(this.selectionBarContainer)

    // 创建浮动按钮容器
    this.floatingButtonContainer = document.createElement('div')
    this.floatingButtonContainer.id = 'web-assistant-floating-button'
    this.floatingButtonContainer.className =
      'web-assistant-floating-button-container'
    this.shadowRoot.appendChild(this.floatingButtonContainer)

    // 创建AIProcessModal容器
    this.aiProcessModalContainer = document.createElement('div')
    this.aiProcessModalContainer.id = 'web-assistant-ai-process-modal'
    this.aiProcessModalContainer.className =
      'web-assistant-ai-process-modal-container'
    this.shadowRoot.appendChild(this.aiProcessModalContainer)

    // 创建隐藏ChatUI容器
    this.hiddenChatUIContainer = document.createElement('div')
    this.hiddenChatUIContainer.id = 'web-assistant-hidden-chatui'
    this.hiddenChatUIContainer.className = 'web-assistant-hidden-chatui-container'
    this.shadowRoot.appendChild(this.hiddenChatUIContainer)

    // 添加到页面
    document.body.appendChild(this.mainContainer)
  }

  /**
   * 初始化事件监听器
   */
  private initEventListeners(): void {
    // 监听文本选择
    document.addEventListener('mouseup', this.handleMouseUp)
    document.addEventListener('keyup', this.handleKeyUp)

    // 监听点击事件（用于隐藏工具栏）
    document.addEventListener('click', this.handleDocumentClick)

    // 监听选择变化
    document.addEventListener('selectionchange', this.handleSelectionChange)

    // 监听页面卸载，清理所有弹窗
    window.addEventListener('beforeunload', this.hideAllModals)

    // 监听页面可见性事件，清理所有弹窗
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // this.hideAllModals()
      } else {
        this.handleFloatingButton()
      }
    })
  }

  /**
   * 处理鼠标抬起事件
   */
  private handleMouseUp = (event: MouseEvent): void => {
    console.log('Mouse up event triggered')

    // 延迟处理，确保选择已完成
    setTimeout(() => {
      this.checkSelection(event)
    }, 10)
  }

  /**
   * 处理键盘事件
   */
  private handleKeyUp = (event: KeyboardEvent): void => {
    // 延迟处理，确保选择已完成
    setTimeout(() => {
      this.checkSelection()
    }, 10)
  }

  /**
   * 处理选择变化事件
   */
  private handleSelectionChange = (): void => {
    // 延迟处理，避免在选择过程中过早隐藏工具栏
    setTimeout(() => {
      const selection = window.getSelection()
      if (!selection || selection.toString().trim() === '') {
        console.log('Selection cleared, hiding toolbar')
        this.hideSelectionBar()
      }
    }, 100)
  }

  /**
   * 处理文档点击事件
   */
  private handleDocumentClick = (event: MouseEvent): void => {
    if (this.isSelectionBarVisible && this.selectionBarContainer) {
      const target = event.target as Node
      const eventPath = event.composedPath()

      const isInsideMainContainer =
        this.mainContainer && eventPath.includes(this.mainContainer)
      const isInsideShadowDOM =
        this.shadowRoot &&
        eventPath.some((node) => {
          // 确保 node 是 Node 类型再调用 contains
          return node instanceof Node && this.shadowRoot!.contains(node)
        })
      const isInsideSelectionBar =
        this.selectionBarContainer &&
        eventPath.some((node) => {
          // 确保 node 是 Node 类型再调用 contains
          return node instanceof Node && this.selectionBarContainer!.contains(node)
        })

      // 如果点击在我们的组件内部，不隐藏工具栏
      if (isInsideShadowDOM || isInsideMainContainer || isInsideSelectionBar) {
        console.log('Click inside selection bar area, not hiding')
        return
      }

      // 检查是否还有选中的文本
      const selection = window.getSelection()
      if (!selection || selection.toString().trim() === '') {
        console.log('Hiding selection bar due to outside click')
        this.hideSelectionBar()
      }
    }
  }

  /**
   * 检查当前选择
   */
  private checkSelection(event?: MouseEvent): void {
    const selection = window.getSelection()
    if (!selection) return

    const newSelectedText = selection.toString().trim()

    if (newSelectedText && newSelectedText !== this.selectedText) {
      this.selectedText = newSelectedText
      this.showSelectionBar(event)
    } else if (!newSelectedText && this.isSelectionBarVisible) {
      // 简化逻辑：选择清空时直接隐藏SelectionBar
      // AIProcessModal有自己独立的生命周期管理
      this.hideSelectionBar()
    }
  }

  /**
   * 显示划词工具栏
   */
  private showSelectionBar(event?: MouseEvent): void {
    if (!this.selectedText) return

    console.log(
      'WebAssistantManager: Showing selection bar for text:',
      this.selectedText
    )

    // 预先计算并缓存选择区域的位置信息，避免后续操作时选择状态丢失
    this.cacheSelectionPosition()

    // 如果容器不存在或已被移除，重新创建
    if (!this.selectionBarContainer || !this.selectionBarContainer.parentNode) {
      this.createSelectionBarContainer()
    }

    // 创建 React root（如果还没有）
    if (!this.selectionBarRoot) {
      this.selectionBarRoot = createRoot(this.selectionBarContainer)
    }

    // 渲染组件
    this.selectionBarRoot.render(
      React.createElement(SelectionBarComponent, {
        selectedText: this.selectedText,
        onAction: this.handleSelectionBarAction,
        onClose: this.hideSelectionBar,
      })
    )

    // 定位工具栏
    this.positionSelectionBar()

    // 显示工具栏（保持原有的动画效果）
    this.selectionBarContainer.classList.add('show')
    this.isSelectionBarVisible = true
  }

  /**
   * 创建悬浮球
   * @returns 
   */
  private createFloatingButton() {
    if (!this.floatingButtonContainer) return

    this.floatingButtonRoot = createRoot(this.floatingButtonContainer)
    const FloatingButton = getFloatingButton({
      onAction: this.handleFloatingButtonAction,
    })

    // 渲染组件
    this.floatingButtonRoot.render(FloatingButton)
  }

  /**
   * 初始化悬浮按钮
   */
  private async initFloatingButton(): Promise<void> {
    const showFloatBtn = await configShowFloatingButton()
    if (!showFloatBtn) {
      return
    }
    if (!this.floatingButtonRoot) {
      this.createFloatingButton()
    }
  }

  /**
 * 清理悬浮按钮实例
 */
  private async handleFloatingButton() {
    const showFloatBtn = await configShowFloatingButton()
    if (showFloatBtn) {
      if (!this.floatingButtonRoot) {   // 如果没有悬浮按钮，则创建悬浮按钮
        this.createFloatingButton()
      }
      return
    }
    if (this.floatingButtonRoot) {
      try {
        this.floatingButtonRoot.unmount()
        this.floatingButtonRoot = null
      } catch (error) {
        console.error('WebAssistantManager: Failed to cleanup FloatingButton:', error)
      }
    }
  }

  /**
   * 初始化隐藏的ChatUI实例
   */
  private initHiddenChatUI(): void {
    if (!this.hiddenChatUIContainer) {
      console.error('WebAssistantManager: Hidden ChatUI container not found')
      return
    }

    try {
      // 创建 React root
      this.hiddenChatUIRoot = createRoot(this.hiddenChatUIContainer)

      // 渲染隐藏的ChatUI组件
      this.hiddenChatUIRoot.render(
        React.createElement(HiddenChatUI, {
          ref: this.hiddenChatUIRef,
          selectedText: this.selectedText,
        })
      )
    } catch (error) {
      console.error('WebAssistantManager: Failed to initialize hidden ChatUI:', error)
    }
  }

  /**
   * 获取隐藏ChatUI的引用，用于调用onSend方法
   */
  public getHiddenChatUIRef(): HiddenChatUIRef | null {
    return this.hiddenChatUIRef.current
  }

  /**
   * 直接调用隐藏ChatUI的onSend方法
   */
  public async callHiddenChatUIOnsend(type: string, content: string, options?: any, attachments?: any[]): Promise<any> {
    const chatUIRef = this.getHiddenChatUIRef()

    if (!chatUIRef) {
      throw new Error('Hidden ChatUI not initialized')
    }

    try {
      console.log('callHiddenChatUIOnsend');

      const result = await chatUIRef.chatContext.onSend(type, content, options, attachments)
      return result
    } catch (error) {
      console.error('chatUIRef.chatContext.onSend 请求出错', error);
      throw error
    }
  }

  /**
   * 隐藏划词工具栏
   */
  private hideSelectionBar = (): void => {
    if (this.selectionBarContainer && this.isSelectionBarVisible) {
      // 添加隐藏动画类
      this.selectionBarContainer.classList.remove('show')
      this.selectionBarContainer.classList.add('hide')

      // 等待动画完成后移除DOM元素
      setTimeout(() => {
        if (this.selectionBarContainer && this.selectionBarContainer.parentNode) {
          this.selectionBarContainer.remove()
        }

        // 清理React root
        if (this.selectionBarRoot) {
          try {
            this.selectionBarRoot.unmount()
          } catch (error) {
            console.warn('Error unmounting selection bar:', error)
          }
          this.selectionBarRoot = null
        }
      }, 200) // 与CSS动画时长匹配

      this.isSelectionBarVisible = false
      // 保留selectedText和cachedSelectionRect给AIProcessModal使用
    }
  }

  /**
   * 创建划词工具栏容器
   */
  private createSelectionBarContainer(): void {
    // 创建划词工具栏容器
    this.selectionBarContainer = document.createElement('div')
    this.selectionBarContainer.id = 'web-assistant-selection-bar'
    this.selectionBarContainer.className = 'web-assistant-selection-bar-container'
    this.shadowRoot.appendChild(this.selectionBarContainer)
  }

  /**
   * 显示AIProcessModal
   */
  private showAIProcessModal(action: string): void {
    if (!this.selectedText) return

    // 设置当前AI操作和状态
    this.currentAIAction = action
    this.isAIProcessModalVisible = true

    // 先隐藏SelectionBar（实现互斥显示）
    this.hideSelectionBar()

    // 如果容器不存在或已被移除，重新创建
    if (!this.aiProcessModalContainer || !this.aiProcessModalContainer.parentNode) {
      this.createAIProcessModalContainer()
    }

    // 创建React root（如果还没有）
    if (!this.aiProcessModalRoot) {
      this.aiProcessModalRoot = createRoot(this.aiProcessModalContainer)
    }

    // 渲染AIProcessModal组件（不传递position，由容器定位控制）
    this.aiProcessModalRoot.render(
      React.createElement(AIProcessModalComponent, {
        isVisible: true,
        selectedText: this.selectedText,
        actionType: this.currentAIAction,
        onClose: this.hideAIProcessModal,
        onRegisterDataUpdater: this.handleAIProcessModalDataUpdate,
      })
    )

    // 定位弹窗（使用与SelectionBar相同的方式）
    this.positionAIProcessModal()
  }

  /**
   * 处理 AIProcessModal 数据更新
   */
  private handleAIProcessModalDataUpdate = (updater: (data: string, isComplete: boolean, conversationId?: string) => void): void => {
    this.aiProcessModalDataUpdater = updater;
  }

  /**
   * 更新 AIProcessModal 数据（供 action handler 调用）
   */
  public updateAIProcessModalData = (data: string, isComplete: boolean, conversationId?: string): void => {
    if (this.aiProcessModalDataUpdater) {
      this.aiProcessModalDataUpdater(data, isComplete, conversationId);
    }
  }

  /**
   * 隐藏AIProcessModal
   */
  private hideAIProcessModal = (): void => {
    if (this.aiProcessModalContainer && this.isAIProcessModalVisible) {
      // 直接移除DOM元素
      this.aiProcessModalContainer.remove()

      // 清理React root
      if (this.aiProcessModalRoot) {
        try {
          this.aiProcessModalRoot.unmount()
        } catch (error) {
          console.warn('Error unmounting AI process modal:', error)
        }
        this.aiProcessModalRoot = null
      }

      // 重置状态
      this.isAIProcessModalVisible = false
      this.currentAIAction = ''
      this.selectedText = ''
      this.cachedSelectionRect = null // 清理缓存的选择位置
    }
  }

  /**
   * 创建AI处理弹窗容器
   */
  private createAIProcessModalContainer(): void {
    // 创建AIProcessModal容器
    this.aiProcessModalContainer = document.createElement('div')
    this.aiProcessModalContainer.id = 'web-assistant-ai-process-modal'
    this.aiProcessModalContainer.className = 'web-assistant-ai-process-modal-container'
    this.shadowRoot.appendChild(this.aiProcessModalContainer)
  }

  /**
   * 缓存当前选择区域的位置信息
   */
  private cacheSelectionPosition(): void {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) {
      this.cachedSelectionRect = null
      return
    }

    const range = selection.getRangeAt(0)
    this.cachedSelectionRect = range.getBoundingClientRect()
  }

  /**
   * 定位AI处理弹窗（使用与SelectionBar相同的方式）
   */
  private positionAIProcessModal(): void {
    if (!this.aiProcessModalContainer) return

    // 优先使用缓存的选择位置，如果没有则尝试获取当前选择
    let rect = this.cachedSelectionRect

    if (!rect) {
      const selection = window.getSelection()
      if (!selection || selection.rangeCount === 0) {
        console.warn(
          'WebAssistantManager: No selection found and no cached position, using default position'
        )
        // 使用默认位置
        this.aiProcessModalContainer.style.left = '50px'
        this.aiProcessModalContainer.style.top = '50px'
        return
      }
      const range = selection.getRangeAt(0)
      rect = range.getBoundingClientRect()
    }

    // 使用与SelectionBar相同的定位逻辑
    const offsetY = 8
    const modalWidth = 500 // AI弹窗的实际宽度
    const modalHeight = 400 // AI弹窗的估算高度

    let left = rect.left
    let top = rect.bottom + offsetY

    // 确保弹窗不会超出视窗右边界
    const viewportWidth = window.innerWidth
    if (left + modalWidth > viewportWidth) {
      left = rect.right - modalWidth
    }

    // 确保弹窗不会超出视窗下边界
    const viewportHeight = window.innerHeight
    if (top + modalHeight > viewportHeight) {
      top = rect.top - modalHeight - offsetY
    }

    // 确保不会超出左边界和上边界
    left = Math.max(5, left)
    top = Math.max(5, top)

    // 直接设置容器位置（与SelectionBar相同的方式）
    this.aiProcessModalContainer.style.left = `${left}px`
    this.aiProcessModalContainer.style.top = `${top}px`
  }

  /**
   * 隐藏所有弹窗（用于清理状态）
   */
  private hideAllModals(): void {
    this.hideSelectionBar()
    this.hideAIProcessModal()
    this.cleanupHiddenChatUI()
  }

  /**
   * 清理隐藏的ChatUI实例
   */
  private cleanupHiddenChatUI(): void {
    if (this.hiddenChatUIRoot) {
      try {
        this.hiddenChatUIRoot.unmount()
        this.hiddenChatUIRoot = null
      } catch (error) {
        console.error('WebAssistantManager: Failed to cleanup hidden ChatUI:', error)
      }
    }
  }

  /**
   * 设置AIProcessModal的显示状态（保留用于兼容性）
   */
  public setAIProcessModalVisible(visible: boolean): void {
    this.isAIProcessModalVisible = visible
  }

  /**
   * 定位划词工具栏
   */
  private positionSelectionBar(): void {
    if (!this.selectionBarContainer) return

    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return

    const range = selection.getRangeAt(0)
    const rect = range.getBoundingClientRect()

    // 计算悬浮球的位置，让它出现在选中文本的下方
    const offsetY = 8 // 垂直偏移，避免紧贴文本
    const barWidth = 300 // 估算的悬浮球宽度
    const barHeight = 50 // 估算的悬浮球高度

    // 在shadow DOM中，主容器使用固定定位，选择栏容器使用绝对定位
    // rect已经是相对于视口的位置，直接使用即可
    let left = rect.left
    let top = rect.bottom + offsetY

    // 确保悬浮球不会超出视窗右边界
    const viewportWidth = window.innerWidth
    if (left + barWidth > viewportWidth) {
      // 如果超出右边界，调整到选中文本的右边界对齐
      left = rect.right - barWidth
    }

    // 确保悬浮球不会超出视窗下边界
    const viewportHeight = window.innerHeight
    if (top + barHeight > viewportHeight) {
      // 如果超出下边界，显示在选中文本的上方
      top = rect.top - barHeight - offsetY
    }

    // 确保不会超出左边界和上边界
    left = Math.max(5, left)
    top = Math.max(5, top)

    this.selectionBarContainer.style.left = `${left}px`
    this.selectionBarContainer.style.top = `${top}px`
  }

  /**
   * 处理划词工具栏动作
   */
  private handleSelectionBarAction = (action: string): void => {
    console.log('handleSelectionBarAction:', action);


    // 导入isAIAction函数来判断是否为AI操作
    const { isAIAction } = require('../../config/menuItems')

    if (isAIAction(action)) {
      // AI操作：显示AIProcessModal，隐藏SelectionBar
      this.showAIProcessModal(action)
      //调起hideChatui的onSend
      this.callHiddenChatUIOnsend('text', `${this.selectedText}`, {
        agentId: action, // 使用传入的action参数作为agentId，而不是硬编码
        conversationId: "",
        extendParams: {
          msgChannel: 'contentScript',
          // translateOptions: {
          //   src_lang: 'en',
          //   tgt_lang: 'zh'
          // },
        }
      })
    } else {
      // 非AI操作：保持原有逻辑
      switch (action) {
        case 'open-panel':
          this.handleOpenPanel()
          break
        default:
          console.log('WebAssistantManager: Unknown action:', action)
      }
    }
  }

  /**
   * 处理浮动按钮动作
   */
  private handleFloatingButtonAction = (
    action: EFloatButtonActionType
  ): void => {
    switch (action) {
      case EFloatButtonActionType.Summary:
        this.handleSummary()
        break
      case EFloatButtonActionType.Translate:
        this.handleTranslate()
        break
      case EFloatButtonActionType.Screenshot:
        this.handleScreenshot()
        break
      case EFloatButtonActionType.OpenPanel:
        this.handleOpenPanel()
        break
      default:
        console.log(
          'WebAssistantManager: Unknown floating button action:',
          action
        )
    }
  }

  private handleFloatingButtonSendMessage(data: any): void {
    chrome.runtime.sendMessage({
      type: EContentsMessageType.FloatingButton,
      data,
    })
  }

  /**
   * 处理打开面板
   */
  private handleOpenPanel(): void {
    this.handleFloatingButtonSendMessage({
      action: EFloatButtonActionType.OpenPanel,
    })
  }

  /**
   * 处理截图功能
   */
  private handleScreenshot(): void {
    this.handleFloatingButtonSendMessage({
      action: EFloatButtonActionType.Screenshot,
    })

    // 向背景脚本发送消息，请求打开侧边栏面板
    chrome.runtime
      .sendMessage({
        action: 'open-panel',
        text: this.selectedText,
      })
      .then(() => {
        // 关闭划词工具栏
        this.hideSelectionBar()
      })
      .catch((error) => {
        console.error(
          'WebAssistantManager: Failed to send panel open request:',
          error
        )
      })
  }

  private async handleSummary(): Promise<void> {
    this.handleFloatingButtonSendMessage({
      action: EFloatButtonActionType.Summary,
    })
  }

  private async handleTranslate(): Promise<void> {
    try {
      handleStartTranslate()

    } catch (error) {
      console.error('WebAssistantManager: Translate failed:', error)
      // 降级到原有的事件传递机制
      this.handleFloatingButtonSendMessage({
        action: EFloatButtonActionType.Translate,
      })
    }
  }

  /**
   * 获取样式
   */
  private getStyles(): string {
    return `
      /* Web Assistant 主样式文件 */

      /* 重置样式 */
      * {
        box-sizing: border-box;
      }

      /* 主容器样式 */
      .web-assistant-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        z-index: 2147483647;
        pointer-events: none;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      /* 划词工具栏容器 */
      .web-assistant-selection-bar-container {
        position: absolute;
        opacity: 0;
        visibility: hidden;
        pointer-events: auto;
        transform: translateY(-10px) scale(0.95);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .web-assistant-selection-bar-container.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0) scale(1);
      }

      .web-assistant-selection-bar-container.hide {
        opacity: 0;
        visibility: hidden;
        transform: translateY(-5px) scale(0.98);
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      }

      /* 浮动按钮容器 */
      .web-assistant-floating-button-container {
        position: fixed;
        pointer-events: auto;
        z-index: 2147483647;
      }

      /* AIProcessModal容器 */
      .web-assistant-ai-process-modal-container {
        position: absolute;
        pointer-events: auto;
        z-index: 10001; /* 比SelectionBar更高的层级 */
      }

      /* 确保所有子元素都有正确的指针事件 */
      #web-assistant-selection-bar,
      #web-assistant-floating-button,
      #web-assistant-ai-process-modal {
        pointer-events: auto;
      }
    `
  }
}

// 防止重复初始化
declare global {
  interface Window {
    webAssistantManagerInitialized?: boolean
    webAssistantManager?: any  // 使用 any 避免循环依赖问题
  }
}

// 初始化 Web Assistant Manager
if (!window.webAssistantManagerInitialized) {
  window.webAssistantManagerInitialized = true
  window.webAssistantManager = new WebAssistantManager()
} else {
  console.log('WebAssistantManager: Already initialized, skipping...')
}
