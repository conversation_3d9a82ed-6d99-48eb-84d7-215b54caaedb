.languageSelector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  margin: 4px 0;
}

.languageDropdown {
  position: relative;
  flex: 1;
}

.languageButton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  font-size: 12px;

  &:hover {
    border-color: #4096ff;
    box-shadow: 0 0 0 2px rgba(64, 150, 255, 0.1);
  }
}

.languageLabel {
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 50px;
}

.dropdownArrow {
  color: #9ca3af;
  font-size: 10px;
  margin-left: 4px;
  transition: transform 0.2s ease;
}

.languageButton:hover .dropdownArrow {
  color: #4096ff;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10001;
  margin-top: 2px;
  max-height: 200px;
  overflow-y: auto;

  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

.dropdownItem {
  padding: 6px 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 12px;
  color: #374151;

  &:hover {
    background-color: #f3f4f6;
  }

  &.selected {
    background-color: #eff6ff;
    color: #2563eb;
    font-weight: 500;
  }

  &:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  &:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.swapButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #6b7280;

  &:hover:not(.disabled) {
    border-color: #4096ff;
    color: #4096ff;
    background: #f0f9ff;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    color: #9ca3af;
  }
}

/* 响应式调整 */
@media (max-width: 480px) {
  .languageSelector {
    gap: 4px;
    padding: 6px 8px;
  }

  .languageButton {
    padding: 3px 6px;
    min-width: 50px;
  }

  .languageLabel {
    max-width: 40px;
    font-size: 11px;
  }

  .swapButton {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}
