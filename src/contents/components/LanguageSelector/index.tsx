import React, { useState, useRef, useEffect } from 'react';
import { getSourceLanguageOptions, getTargetLanguageOptions, type LanguageOption } from '../../../config/languageConfig';
import * as styles from './index.module.less';

export interface LanguageSelection {
  srcLang: string;
  tgtLang: string;
}

interface LanguageSelectorProps {
  onLanguageChange: (selection: LanguageSelection) => void;
  defaultSrcLang?: string;
  defaultTgtLang?: string;
  className?: string;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  onLanguageChange,
  defaultSrcLang = 'auto',
  defaultTgtLang = 'zh',
  className = '',
}) => {
  const [srcLang, setSrcLang] = useState(defaultSrcLang);
  const [tgtLang, setTgtLang] = useState(defaultTgtLang);
  const [showSrcDropdown, setShowSrcDropdown] = useState(false);
  const [showTgtDropdown, setShowTgtDropdown] = useState(false);

  const srcDropdownRef = useRef<HTMLDivElement>(null);
  const tgtDropdownRef = useRef<HTMLDivElement>(null);

  const sourceOptions = getSourceLanguageOptions();
  const targetOptions = getTargetLanguageOptions();

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (srcDropdownRef.current && !srcDropdownRef.current.contains(event.target as Node)) {
        setShowSrcDropdown(false);
      }
      if (tgtDropdownRef.current && !tgtDropdownRef.current.contains(event.target as Node)) {
        setShowTgtDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 语言变更时通知父组件
  useEffect(() => {
    onLanguageChange({ srcLang, tgtLang });
  }, [srcLang, tgtLang, onLanguageChange]);

  const handleSrcLanguageSelect = (option: LanguageOption) => {
    setSrcLang(option.value);
    setShowSrcDropdown(false);
  };

  const handleTgtLanguageSelect = (option: LanguageOption) => {
    setTgtLang(option.value);
    setShowTgtDropdown(false);
  };

  const getSrcLanguageLabel = () => {
    const option = sourceOptions.find(opt => opt.value === srcLang);
    return option?.label || srcLang;
  };

  const getTgtLanguageLabel = () => {
    const option = targetOptions.find(opt => opt.value === tgtLang);
    return option?.label || tgtLang;
  };

  const handleSwapLanguages = () => {
    if (srcLang === 'auto') return; // 自动检测不能作为目标语言
    
    const newSrcLang = tgtLang;
    const newTgtLang = srcLang;
    setSrcLang(newSrcLang);
    setTgtLang(newTgtLang);
  };

  return (
    <div className={`${styles.languageSelector} ${className}`}>
      {/* 源语言选择 */}
      <div className={styles.languageDropdown} ref={srcDropdownRef}>
        <div
          className={styles.languageButton}
          onClick={(e) => {
            e.stopPropagation();
            setShowSrcDropdown(!showSrcDropdown);
            setShowTgtDropdown(false);
          }}
        >
          <span className={styles.languageLabel}>{getSrcLanguageLabel()}</span>
          <span className={styles.dropdownArrow}>▼</span>
        </div>
        {showSrcDropdown && (
          <div className={styles.dropdownMenu}>
            {sourceOptions.map((option) => (
              <div
                key={option.value}
                className={`${styles.dropdownItem} ${srcLang === option.value ? styles.selected : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleSrcLanguageSelect(option);
                }}
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 交换按钮 */}
      <div 
        className={`${styles.swapButton} ${srcLang === 'auto' ? styles.disabled : ''}`}
        onClick={(e) => {
          e.stopPropagation();
          handleSwapLanguages();
        }}
        title={srcLang === 'auto' ? '自动检测不能交换' : '交换语言'}
      >
        ⇄
      </div>

      {/* 目标语言选择 */}
      <div className={styles.languageDropdown} ref={tgtDropdownRef}>
        <div
          className={styles.languageButton}
          onClick={(e) => {
            e.stopPropagation();
            setShowTgtDropdown(!showTgtDropdown);
            setShowSrcDropdown(false);
          }}
        >
          <span className={styles.languageLabel}>{getTgtLanguageLabel()}</span>
          <span className={styles.dropdownArrow}>▼</span>
        </div>
        {showTgtDropdown && (
          <div className={styles.dropdownMenu}>
            {targetOptions.map((option) => (
              <div
                key={option.value}
                className={`${styles.dropdownItem} ${tgtLang === option.value ? styles.selected : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleTgtLanguageSelect(option);
                }}
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default LanguageSelector;
