/**
 * 语言配置模块
 * 统一管理翻译功能中的语言选项
 */

export interface LanguageOption {
  label: string;
  value: string;
}

export interface TranslateLanguageOptions {
  src_lang: {
    defaultValue: string;
    options: LanguageOption[];
  };
  tgt_lang: {
    defaultValue: string;
    options: LanguageOption[];
  };
}

/**
 * 支持的语言列表
 */
export const LANGUAGE_OPTIONS: LanguageOption[] = [
  {
    label: "中文",
    value: "zh",
  },
  {
    label: "英文", 
    value: "en",
  },
  {
    label: "日文",
    value: "ja",
  },
  {
    label: "韩文",
    value: "ko",
  },
  {
    label: "法文",
    value: "fr",
  },
  {
    label: "德文",
    value: "de",
  },
  {
    label: "西班牙文",
    value: "es",
  },
  {
    label: "意大利文",
    value: "it",
  },
  {
    label: "俄文",
    value: "ru",
  },
  {
    label: "葡萄牙文",
    value: "pt",
  },
];

/**
 * 默认翻译语言配置
 */
export const DEFAULT_TRANSLATE_OPTIONS: TranslateLanguageOptions = {
  src_lang: {
    defaultValue: 'auto', // 自动检测源语言
    options: [
      {
        label: "自动检测",
        value: "auto",
      },
      ...LANGUAGE_OPTIONS,
    ],
  },
  tgt_lang: {
    defaultValue: 'zh',
    options: LANGUAGE_OPTIONS,
  },
};

/**
 * 获取语言选项列表
 */
export const getLanguageOptions = (): LanguageOption[] => {
  return LANGUAGE_OPTIONS;
};

/**
 * 获取源语言选项列表（包含自动检测）
 */
export const getSourceLanguageOptions = (): LanguageOption[] => {
  return DEFAULT_TRANSLATE_OPTIONS.src_lang.options;
};

/**
 * 获取目标语言选项列表
 */
export const getTargetLanguageOptions = (): LanguageOption[] => {
  return DEFAULT_TRANSLATE_OPTIONS.tgt_lang.options;
};

/**
 * 根据语言代码获取语言标签
 */
export const getLanguageLabel = (value: string): string => {
  const allOptions = [
    { label: "自动检测", value: "auto" },
    ...LANGUAGE_OPTIONS,
  ];
  const option = allOptions.find(opt => opt.value === value);
  return option?.label || value;
};

/**
 * 获取默认的翻译选项
 */
export const getDefaultTranslateOptions = (): TranslateLanguageOptions => {
  return DEFAULT_TRANSLATE_OPTIONS;
};
